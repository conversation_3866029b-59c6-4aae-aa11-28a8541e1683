export interface User {
  id: number;
  username: string;
  email: string;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

export interface Task {
  id: number;
  title: string;
  description?: string;
  due_date?: string;
  completed: boolean;
  completed_at?: string;
  created_at: string;
  updated_at?: string;
  user_id: number;
}

export interface Group {
  id: number;
  name: string;
  description?: string;
  created_at: string;
  updated_at?: string;
  owner_id: number;
  members: User[];
}

export interface AuthTokens {
  access_token: string;
  token_type: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
}

export interface TaskCreate {
  title: string;
  description?: string;
  due_date?: string;
}

export interface TaskUpdate {
  title?: string;
  description?: string;
  due_date?: string;
  completed?: boolean;
}

export interface Character {
  level: number;
  experience: number;
  equipment: {
    weapon?: string;
    armor?: string;
    accessory?: string;
  };
  stats: {
    strength: number;
    defense: number;
    speed: number;
  };
}

export interface Animation {
  type: 'attack' | 'levelUp' | 'idle' | 'celebrate';
  duration: number;
  isPlaying: boolean;
}
