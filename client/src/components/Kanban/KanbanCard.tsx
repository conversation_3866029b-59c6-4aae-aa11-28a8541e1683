import React, { useRef, useEffect } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

export interface Task {
  id: string;
  title: string;
  description: string;
}

interface KanbanCardProps {
  task: Task;
}

export const KanbanCard: React.FC<KanbanCardProps> = ({ task }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: task.id });

  const audioRef = useRef<HTMLAudioElement | null>(null);
  const wasDraggingRef = useRef(false);

  // Initialize audio element
  useEffect(() => {
    audioRef.current = new Audio('/static/sounds/ui-button-press.wav');
    audioRef.current.preload = 'auto';
  }, []);

  // Track dragging state and play sound when dragging ends
  useEffect(() => {
    if (isDragging) {
      wasDraggingRef.current = true;
    } else if (wasDraggingRef.current) {
      // Dragging just ended, play sound
      wasDraggingRef.current = false;
      if (audioRef.current) {
        audioRef.current.currentTime = 0; // Reset to start
        audioRef.current.play().catch(console.error);
      }
    }
  }, [isDragging]);

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="task-card"
    >
      <h4>{task.title}</h4>
      <p>{task.description}</p>
    </div>
  );
};
