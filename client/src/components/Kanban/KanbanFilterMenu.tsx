import { useState } from 'react';
import { Dropdown, DropdownTrigger, DropdownContent, DropdownItem, DropdownSeparator, DropdownLabel } from '@DS/core';
import { TaskForm } from '../Dashboard/TaskForm';
import { useTasks } from '../../hooks/useTasks';
import { TaskCreate } from '../../types';

export const KanbanFilterMenu = () => {
    const [showTaskForm, setShowTaskForm] = useState(false);
    const { createTask, isLoading } = useTasks();

    const handleCreateTask = async (data: TaskCreate) => {
        await createTask(data);
        setShowTaskForm(false);
    };

    return (
        <>
            <div className="flex gap-2 mb-4">
                <Dropdown>
                    <DropdownTrigger asChild>
                        <button className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md text-sm font-medium">
                            Filter Tasks
                        </button>
                    </DropdownTrigger>
                    <DropdownContent>
                        <DropdownLabel>Filter by Status</DropdownLabel>
                        <DropdownItem onSelect={() => console.log('Show All')}>
                            Show All
                        </DropdownItem>
                        <DropdownItem onSelect={() => console.log('To Do')}>
                            To Do
                        </DropdownItem>
                        <DropdownItem onSelect={() => console.log('Overdue')}>
                            Overdue
                        </DropdownItem>
                        <DropdownItem onSelect={() => console.log('Completed')}>
                            Completed
                        </DropdownItem>
                        <DropdownSeparator />
                        <DropdownItem onSelect={() => console.log('Clear Filters')}>
                            Clear Filters
                        </DropdownItem>
                    </DropdownContent>
                </Dropdown>

                <button
                    onClick={() => setShowTaskForm(true)}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium"
                >
                    + Add Task
                </button>
            </div>

            {showTaskForm && (
                <TaskForm
                    onSubmit={handleCreateTask}
                    onCancel={() => setShowTaskForm(false)}
                    isLoading={isLoading}
                />
            )}
        </>
    )
}
