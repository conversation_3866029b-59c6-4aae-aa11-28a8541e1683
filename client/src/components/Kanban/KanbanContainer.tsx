import React, { useState, useRef, useEffect } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { KanbanColumn, KanbanCard, Task, Column } from './index';

interface KanbanContainerProps {
  initialColumns: Column[];
  onTaskMove?: (taskId: string, fromColumnId: string, toColumnId: string) => Promise<void>;
  onTaskReorder?: (taskId: string, columnId: string, newIndex: number) => Promise<void>;
}

export const KanbanContainer: React.FC<KanbanContainerProps> = ({
  initialColumns,
  onTaskMove,
  onTaskReorder
}) => {
  const [columns, setColumns] = useState<Column[]>(initialColumns);
  const [activeTask, setActiveTask] = useState<Task | null>(null);

  // Audio refs for sound effects
  const pressAudioRef = useRef<HTMLAudioElement | null>(null);
  const successAudioRef = useRef<HTMLAudioElement | null>(null);

  // Initialize audio elements
  useEffect(() => {
    pressAudioRef.current = new Audio('/static/sounds/ui-press.wav');
    pressAudioRef.current.preload = 'auto';

    successAudioRef.current = new Audio('/static/sounds/ui-success.wav');
    successAudioRef.current.preload = 'auto';
  }, []);

  // Update columns when initialColumns change (from backend)
  React.useEffect(() => {
    setColumns(initialColumns);
  }, [initialColumns]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const findTaskById = (id: string): Task | null => {
    for (const column of columns) {
      const task = column.tasks.find((task) => task.id === id);
      if (task) return task;
    }
    return null;
  };

  const findColumnByTaskId = (taskId: string): Column | null => {
    return columns.find((column) =>
      column.tasks.some((task) => task.id === taskId)
    ) || null;
  };

  const findColumnById = (columnId: string): Column | null => {
    return columns.find((column) => column.id === columnId) || null;
  };

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const task = findTaskById(active.id as string);
    setActiveTask(task);
  };

  const handleDragOver = (event: DragOverEvent) => {
    const { active, over } = event;
    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    // Find the columns
    const activeColumn = findColumnByTaskId(activeId);
    const overColumn = findColumnByTaskId(overId) || findColumnById(overId);

    if (!activeColumn || !overColumn) return;

    // Handle cross-column moves only
    if (activeColumn.id !== overColumn.id) {
      // Call backend callback if provided
      if (onTaskMove) {
        onTaskMove(activeId, activeColumn.id, overColumn.id);
      } else {
        // Fallback to local state update if no callback
        setColumns((columns) => {
          const activeItems = activeColumn.tasks;
          const overItems = overColumn.tasks;

          const activeIndex = activeItems.findIndex((item) => item.id === activeId);
          const overIndex = overItems.findIndex((item) => item.id === overId);

          const activeTask = activeItems[activeIndex];

          // Remove from active column
          const newActiveItems = activeItems.filter((item) => item.id !== activeId);

          // Add to over column
          const newOverItems = [...overItems];
          if (overIndex >= 0) {
            // Dropping on a specific task
            newOverItems.splice(overIndex, 0, activeTask);
          } else {
            // Dropping on empty column or at the end
            newOverItems.push(activeTask);
          }

          return columns.map((column) => {
            if (column.id === activeColumn.id) {
              return { ...column, tasks: newActiveItems };
            } else if (column.id === overColumn.id) {
              return { ...column, tasks: newOverItems };
            }
            return column;
          });
        });
      }
    }
  };

  // Play appropriate sound based on drop target
  const playSound = (isDroppedOnDone: boolean) => {
    const audioToPlay = isDroppedOnDone ? successAudioRef.current : pressAudioRef.current;
    if (audioToPlay) {
      audioToPlay.currentTime = 0; // Reset to start
      audioToPlay.play().catch(console.error);
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveTask(null);

    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    // Find the columns
    const activeColumn = findColumnByTaskId(activeId);
    const overColumn = findColumnByTaskId(overId) || findColumnById(overId);

    if (!activeColumn || !overColumn) return;

    // Play sound immediately when drag ends
    playSound(overColumn.id === 'done');

    // Handle same-column reordering
    if (activeColumn.id === overColumn.id) {
      const activeIndex = activeColumn.tasks.findIndex((item) => item.id === activeId);
      const overIndex = activeColumn.tasks.findIndex((item) => item.id === overId);

      if (activeIndex !== overIndex) {
        // Call backend callback if provided
        if (onTaskReorder) {
          onTaskReorder(activeId, activeColumn.id, overIndex);
        } else {
          // Fallback to local state update if no callback
          setColumns((columns) => {
            return columns.map((column) => {
              if (column.id === activeColumn.id) {
                const newTasks = [...column.tasks];
                const [movedTask] = newTasks.splice(activeIndex, 1);
                newTasks.splice(overIndex, 0, movedTask);
                return { ...column, tasks: newTasks };
              }
              return column;
            });
          });
        }
      }
    }
  };

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      <div className="kanban-board">
        {columns.map((column) => (
          <KanbanColumn key={column.id} column={column} />
        ))}
      </div>

      <DragOverlay>
        {activeTask ? (
          <div className="task-card dragging">
            <KanbanCard task={activeTask} />
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  );
};
