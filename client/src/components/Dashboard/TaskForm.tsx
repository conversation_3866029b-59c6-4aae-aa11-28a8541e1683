import React from 'react';
import { useForm } from 'react-hook-form';
import { X } from 'lucide-react';
import { Button } from '../UI/Button';
import { Input } from '../UI/Input';
import { Task, TaskCreate } from '../../types';

interface TaskFormProps {
  task?: Task;
  onSubmit: (data: TaskCreate) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export const TaskForm: React.FC<TaskFormProps> = ({
  task,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<TaskCreate>({
    defaultValues: task
      ? {
          title: task.title,
          description: task.description || '',
          due_date: task.due_date ? new Date(task.due_date).toISOString().slice(0, 16) : '',
        }
      : {
          title: '',
          description: '',
          due_date: '',
        },
  });

  const handleFormSubmit = async (data: TaskCreate) => {
    try {
      // Convert due_date to ISO string if provided
      const formattedData = {
        ...data,
        due_date: data.due_date ? new Date(data.due_date).toISOString() : undefined,
      };
      await onSubmit(formattedData);
    } catch (error) {
      // Error handling is done in the parent component
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-lg font-semibold">
            {task ? 'Edit Task' : 'Create New Task'}
          </h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="p-6 space-y-4">
          <Input
            label="Title"
            {...register('title', { required: 'Title is required' })}
            error={errors.title?.message}
            placeholder="Enter task title"
          />

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              {...register('description')}
              rows={3}
              className="block w-full rounded-md border border-gray-300 px-3 py-2 text-sm placeholder-gray-400 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
              placeholder="Enter task description (optional)"
            />
          </div>

          <Input
            label="Due Date"
            type="datetime-local"
            {...register('due_date')}
            error={errors.due_date?.message}
          />

          <div className="flex space-x-3 pt-4">
            <Button
              type="submit"
              className="flex-1"
              isLoading={isLoading}
            >
              {task ? 'Update Task' : 'Create Task'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
