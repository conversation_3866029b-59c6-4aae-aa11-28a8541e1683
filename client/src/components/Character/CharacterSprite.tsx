import React, { useEffect, useRef } from 'react';
import { Application, Container, Graphics } from 'pixi.js';
import { useCharacterStore } from '../../store/characterStore';

interface CharacterSpriteProps {
  width?: number;
  height?: number;
}

export const CharacterSprite: React.FC<CharacterSpriteProps> = ({
  width = 300,
  height = 400,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const appRef = useRef<Application | null>(null);
  const characterContainerRef = useRef<Container | null>(null);
  const { character, currentAnimation } = useCharacterStore();

  useEffect(() => {
    if (!canvasRef.current) return;

    // Small delay to ensure canvas is fully mounted in the DOM
    const initializePixi = () => {
      const canvas = canvasRef.current;
      if (!canvas) return;

      // Ensure canvas is properly sized before PixiJS initialization
      canvas.width = width;
      canvas.height = height;

      // Initialize PIXI Application with explicit renderer configuration
      const app = new Application({
        view: canvas,
        width,
        height,
        backgroundColor: 0x87CEEB, // Sky blue background
        antialias: false, // Disable antialiasing for better compatibility
        resolution: 1, // Set explicit resolution
        autoDensity: false, // Disable auto density for consistency
        powerPreference: 'default', // Use default power preference
      });

      appRef.current = app;

      // Create character container
      const characterContainer = new Container();
      characterContainer.x = width / 2;
      characterContainer.y = height / 2;
      characterContainerRef.current = characterContainer;
      app.stage.addChild(characterContainer);

      // Create placeholder sprites (since we don't have actual sprite files)
      createPlaceholderCharacter(characterContainer);

      return () => {
        app.destroy(true);
      };
    };

    // Use setTimeout to ensure canvas is fully mounted
    const timeoutId = setTimeout(initializePixi, 0);

    return () => {
      clearTimeout(timeoutId);
      if (appRef.current) {
        appRef.current.destroy(true);
      }
    };
  }, [width, height]);

  useEffect(() => {
    // Update character appearance when equipment changes
    if (characterContainerRef.current) {
      updateCharacterAppearance();
    }
  }, [character.equipment]);

  useEffect(() => {
    // Handle animations
    if (currentAnimation.isPlaying && characterContainerRef.current) {
      playCharacterAnimation(currentAnimation.type);
    }
  }, [currentAnimation]);

  const createPlaceholderCharacter = (container: Container) => {
    // Create base character (circle for head, rectangle for body)
    const head = new Graphics();
    head.beginFill(0xFFDBB3); // Skin color
    head.drawCircle(0, -60, 25);
    head.endFill();

    const body = new Graphics();
    body.beginFill(0x4169E1); // Blue shirt
    body.drawRect(-20, -30, 40, 60);
    body.endFill();

    const legs = new Graphics();
    legs.beginFill(0x8B4513); // Brown pants
    legs.drawRect(-15, 30, 30, 40);
    legs.endFill();

    // Add equipment based on character state
    const weapon = createWeaponSprite(character.equipment.weapon);
    const armor = createArmorSprite(character.equipment.armor);

    container.addChild(head, body, legs);
    if (weapon) container.addChild(weapon);
    if (armor) container.addChild(armor);

    // Add level indicator
    const levelText = new Graphics();
    levelText.beginFill(0xFFD700); // Gold background
    levelText.drawRoundedRect(-15, -100, 30, 20, 5);
    levelText.endFill();
    
    // Note: In a real implementation, you'd use PIXI.Text for the level number
    container.addChild(levelText);
  };

  const createWeaponSprite = (weaponType?: string) => {
    if (!weaponType) return null;

    const weapon = new Graphics();
    weapon.x = 30; // Position to the right of character

    switch (weaponType) {
      case 'sword':
        weapon.beginFill(0xC0C0C0); // Silver
        weapon.drawRect(0, -50, 5, 80);
        weapon.drawRect(-5, -55, 15, 10); // Hilt
        break;
      case 'staff':
        weapon.beginFill(0x8B4513); // Brown
        weapon.drawRect(0, -60, 3, 100);
        weapon.beginFill(0x4169E1); // Blue orb
        weapon.drawCircle(1.5, -65, 8);
        break;
      default:
        return null;
    }
    weapon.endFill();
    return weapon;
  };

  const createArmorSprite = (armorType?: string) => {
    if (!armorType || armorType === 'basic') return null;

    const armor = new Graphics();
    
    switch (armorType) {
      case 'leather':
        armor.beginFill(0x8B4513); // Brown leather
        armor.drawRect(-22, -32, 44, 64);
        break;
      case 'chainmail':
        armor.beginFill(0xC0C0C0); // Silver
        armor.drawRect(-22, -32, 44, 64);
        break;
      default:
        return null;
    }
    armor.endFill();
    return armor;
  };

  const updateCharacterAppearance = () => {
    if (!characterContainerRef.current) return;
    
    // Clear and recreate character with new equipment
    characterContainerRef.current.removeChildren();
    createPlaceholderCharacter(characterContainerRef.current);
  };

  const playCharacterAnimation = (animationType: string) => {
    if (!characterContainerRef.current) return;

    const container = characterContainerRef.current;
    const originalScale = container.scale.x;

    switch (animationType) {
      case 'attack':
        // Quick scale and rotation animation
        container.scale.set(1.1);
        container.rotation = 0.1;
        setTimeout(() => {
          container.scale.set(originalScale);
          container.rotation = 0;
        }, 200);
        break;

      case 'levelUp':
        // Pulsing scale animation
        let pulseCount = 0;
        const pulseInterval = setInterval(() => {
          container.scale.set(pulseCount % 2 === 0 ? 1.2 : 1.0);
          pulseCount++;
          if (pulseCount >= 6) {
            clearInterval(pulseInterval);
            container.scale.set(originalScale);
          }
        }, 200);
        break;

      case 'celebrate':
        // Bounce animation
        let bounceCount = 0;
        const bounceInterval = setInterval(() => {
          container.y = height / 2 + (bounceCount % 2 === 0 ? -20 : 0);
          bounceCount++;
          if (bounceCount >= 6) {
            clearInterval(bounceInterval);
            container.y = height / 2;
          }
        }, 150);
        break;

      default:
        break;
    }
  };

  const characterExperience = (character.experience / (character.level * 100)) * 100

  return (
    <div className="flex flex-col items-center space-y-4">
      <canvas ref={canvasRef} className="border border-gray-300 rounded-lg shadow-lg" />
      
      {/* Character Stats */}
      <div className="bg-white rounded-lg p-4 shadow-md w-full max-w-xs">
        <h3 className="text-lg font-semibold mb-2">Character Stats</h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span>Level:</span>
            <span className="font-bold text-primary-600">{character.level}</span>
          </div>
          <div className="flex justify-between">
            <span>Experience:</span>
            <span>{character.experience}/{character.level * 100}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-primary-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${characterExperience}%` }}
            />
          </div>
          <div className="grid grid-cols-3 gap-2 mt-3">
            <div className="text-center">
              <div className="text-xs text-gray-500">STR</div>
              <div className="font-bold">{character.stats.strength}</div>
            </div>
            <div className="text-center">
              <div className="text-xs text-gray-500">DEF</div>
              <div className="font-bold">{character.stats.defense}</div>
            </div>
            <div className="text-center">
              <div className="text-xs text-gray-500">SPD</div>
              <div className="font-bold">{character.stats.speed}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
