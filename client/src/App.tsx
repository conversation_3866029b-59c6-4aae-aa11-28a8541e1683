import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { LoginForm } from './components/Auth/LoginForm';
import { RegisterForm } from './components/Auth/RegisterForm';
import { ProtectedRoute } from './components/Auth/ProtectedRoute';
import { useAuth } from './hooks/useAuth';
import Home from './pages/Home';

// Component to handle authenticated redirect logic
const AuthenticatedRedirect: React.FC = () => {
  const location = useLocation();

  // Get the redirect path from URL query parameter
  const urlParams = new URLSearchParams(location.search);
  const redirectPath = urlParams.get('path') || '/home';

  return <Navigate to={redirectPath} replace />;
};

function App() {
  const { isAuthenticated } = useAuth();

  return (
    <Router>
      <div className="App">
        <Routes>
          {/* Public routes */}
          <Route
            path="/login"
            element={
              isAuthenticated ? <AuthenticatedRedirect /> : <LoginForm />
            }
          />
          <Route
            path="/register"
            element={
              isAuthenticated ? <AuthenticatedRedirect /> : <RegisterForm />
            }
          />
          
          {/* Protected routes */}
          <Route
            path="/home"
            element={
              <ProtectedRoute>
                <Home />
              </ProtectedRoute>
            }
          />
          
          {/* Default redirect */}
          <Route
            path="/"
            element={
              <Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />
            }
          />
          
          {/* Catch all route */}
          <Route
            path="*"
            element={
              <Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />
            }
          />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
