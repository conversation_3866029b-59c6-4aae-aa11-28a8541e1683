import { useState, useEffect, useCallback } from 'react';
import { useTasks } from './useTasks';
import { Column } from '@/components';
import { 
  transformTasksToKanbanColumns, 
  getTaskIdFromKanbanTask, 
  getTaskStatusFromColumnId 
} from '../utils/kanbanUtils';

export const useKanbanTasks = () => {
  const {
    tasks,
    isLoading,
    error,
    updateTask,
    completeTask,
    clearError,
    refetch
  } = useTasks();

  const [kanbanColumns, setKanbanColumns] = useState<Column[]>([]);

  // Transform backend tasks to Kanban columns whenever tasks change
  useEffect(() => {
    const columns = transformTasksToKanbanColumns(tasks);
    setKanbanColumns(columns);
  }, [tasks]);

  // Handle moving tasks between columns
  const handleTaskMove = useCallback(async (
    taskId: string, 
    fromColumnId: string, 
    toColumnId: string
  ) => {
    const backendTaskId = getTaskIdFromKanbanTask(taskId);
    const newStatus = getTaskStatusFromColumnId(toColumnId);

    try {
      if (toColumnId === 'done') {
        // Complete the task
        await completeTask(backendTaskId);
      } else {
        // Update task status (mark as incomplete if moving from done)
        await updateTask(backendTaskId, { 
          completed: newStatus.completed 
        });
      }
      
      // Refetch tasks to get updated data
      await refetch();
    } catch (error) {
      console.error('Failed to move task:', error);
      // Optionally show error to user
    }
  }, [updateTask, completeTask, refetch]);

  // Handle task reordering within the same column
  const handleTaskReorder = useCallback(async (
    taskId: string,
    columnId: string,
    newIndex: number
  ) => {
    // For now, we'll just update the local state
    // In the future, you could implement task ordering in the backend
    console.log(`Reordering task ${taskId} in column ${columnId} to position ${newIndex}`);
    
    // The KanbanContainer will handle the local state update
    // No backend call needed for reordering within the same status
  }, []);

  return {
    kanbanColumns,
    isLoading,
    error,
    clearError,
    handleTaskMove,
    handleTaskReorder,
    refetch
  };
};
