import { useState, useEffect, useCallback, useRef } from 'react';
import { useTasks } from './useTasks';
import { Column } from '@/components';
import {
  transformTasksToKanbanColumns,
  getTaskIdFromKanbanTask,
  getTaskStatusFromColumnId
} from '../utils/kanbanUtils';

export const useKanbanTasks = () => {
  const {
    tasks,
    isLoading,
    error,
    updateTask,
    completeTask,
    clearError,
    refetch
  } = useTasks();

  const [kanbanColumns, setKanbanColumns] = useState<Column[]>([]);

  // Audio refs for sound effects
  const pressAudioRef = useRef<HTMLAudioElement | null>(null);
  const successAudioRef = useRef<HTMLAudioElement | null>(null);

  // Initialize audio elements
  useEffect(() => {
    pressAudioRef.current = new Audio('/static/sounds/ui-press.wav');
    pressAudioRef.current.preload = 'auto';

    successAudioRef.current = new Audio('/static/sounds/ui-success.wav');
    successAudioRef.current.preload = 'auto';
  }, []);

  // Play appropriate sound based on drop target
  const playSound = useCallback((isDroppedOnDone: boolean) => {
    const audioToPlay = isDroppedOnDone ? successAudioRef.current : pressAudioRef.current;
    if (audioToPlay) {
      audioToPlay.currentTime = 0; // Reset to start
      audioToPlay.play().catch(console.error);
    }
  }, []);

  // Transform backend tasks to Kanban columns whenever tasks change
  useEffect(() => {
    const columns = transformTasksToKanbanColumns(tasks);
    setKanbanColumns(columns);
  }, [tasks]);

  // Handle moving tasks between columns
  const handleTaskMove = useCallback(async (
    taskId: string,
    fromColumnId: string,
    toColumnId: string
  ) => {
    const backendTaskId = getTaskIdFromKanbanTask(taskId);
    const newStatusData = getTaskStatusFromColumnId(toColumnId);

    try {
      if (toColumnId === 'done') {
        // Complete the task (this will also set status to DONE in the backend)
        await completeTask(backendTaskId);
      } else {
        // Update task status and completion state
        await updateTask(backendTaskId, {
          status: newStatusData.status,
          completed: newStatusData.completed
        });
      }

      // Refetch tasks to get updated data
      await refetch();

      // Play appropriate sound based on target column
      playSound(toColumnId === 'done');
    } catch (error) {
      console.error('Failed to move task:', error);
      // Optionally show error to user
    }
  }, [updateTask, completeTask, refetch, playSound]);

  // Handle task reordering within the same column
  const handleTaskReorder = useCallback(async (
    taskId: string,
    columnId: string,
    newIndex: number
  ) => {
    // For reordering within the same column, we don't need to change backend status
    // The KanbanContainer will handle the local state update for visual reordering
    // The in-progress state is maintained separately and doesn't affect ordering
    console.log(`Reordering task ${taskId} in column ${columnId} to position ${newIndex}`);
  }, []);

  return {
    kanbanColumns,
    isLoading,
    error,
    clearError,
    handleTaskMove,
    handleTaskReorder,
    refetch
  };
};
