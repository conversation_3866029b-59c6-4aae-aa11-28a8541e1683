import { useState, useEffect, useCallback } from 'react';
import { useTasks } from './useTasks';
import { Column } from '@/components';
import {
  transformTasksToKanbanColumns,
  getTaskIdFromKanbanTask,
  getTaskStatusFromColumnId
} from '../utils/kanbanUtils';

export const useKanbanTasks = () => {
  const {
    tasks,
    isLoading,
    error,
    updateTask,
    completeTask,
    clearError,
    refetch
  } = useTasks();

  const [kanbanColumns, setKanbanColumns] = useState<Column[]>([]);
  const [inProgressTaskIds, setInProgressTaskIds] = useState<Set<string>>(new Set());

  // Transform backend tasks to Kanban columns whenever tasks change
  useEffect(() => {
    const columns = transformTasksToKanbanColumns(tasks, inProgressTaskIds);
    setKanbanColumns(columns);
  }, [tasks, inProgressTaskIds]);

  // Handle moving tasks between columns
  const handleTaskMove = useCallback(async (
    taskId: string,
    fromColumnId: string,
    toColumnId: string
  ) => {
    const backendTaskId = getTaskIdFromKanbanTask(taskId);

    try {
      // Handle local "In Progress" state management
      if (toColumnId === 'in-progress') {
        // Add to in-progress locally
        setInProgressTaskIds(prev => new Set(prev).add(taskId));
        // If moving from done, mark as incomplete in backend
        if (fromColumnId === 'done') {
          await updateTask(backendTaskId, { completed: false });
          await refetch();
        }
      } else if (fromColumnId === 'in-progress') {
        // Remove from in-progress locally
        setInProgressTaskIds(prev => {
          const newSet = new Set(prev);
          newSet.delete(taskId);
          return newSet;
        });

        if (toColumnId === 'done') {
          // Complete the task
          await completeTask(backendTaskId);
          await refetch();
        }
        // If moving to 'todo', no backend change needed (both are incomplete)
      } else if (toColumnId === 'done') {
        // Complete the task (from todo to done)
        await completeTask(backendTaskId);
        await refetch();
      } else if (fromColumnId === 'done') {
        // Mark as incomplete (from done to todo)
        await updateTask(backendTaskId, { completed: false });
        await refetch();
      }
      // If moving from todo to todo, no action needed

    } catch (error) {
      console.error('Failed to move task:', error);
      // Revert local state on error
      if (toColumnId === 'in-progress') {
        setInProgressTaskIds(prev => {
          const newSet = new Set(prev);
          newSet.delete(taskId);
          return newSet;
        });
      } else if (fromColumnId === 'in-progress') {
        setInProgressTaskIds(prev => new Set(prev).add(taskId));
      }
    }
  }, [updateTask, completeTask, refetch]);

  // Handle task reordering within the same column
  const handleTaskReorder = useCallback(async (
    taskId: string,
    columnId: string,
    newIndex: number
  ) => {
    // For reordering within the same column, we don't need to change backend status
    // The KanbanContainer will handle the local state update for visual reordering
    // The in-progress state is maintained separately and doesn't affect ordering
    console.log(`Reordering task ${taskId} in column ${columnId} to position ${newIndex}`);
  }, []);

  return {
    kanbanColumns,
    isLoading,
    error,
    clearError,
    handleTaskMove,
    handleTaskReorder,
    refetch
  };
};
