/**
 * Color palette for the Kanban RPG application
 * Provides consistent colors across all components
 */

export const colors = {
  // Official Kanban RPG colors
  core: {
    black: "#04080F",
    white: "#F7F9F9",
    blue: "#568EA3",
    green: "#48BF84",
    yellow: "#FFCC00",
    red: "#F06449"
  }
} as const;

// Utility functions for color manipulation
export const colorUtils = {
  /**
   * Get a color with opacity
   */
  withOpacity: (color: string, opacity: number): string => {
    if (color.startsWith('#')) {
      const hex = color.slice(1);
      const alpha = Math.round(opacity * 255).toString(16).padStart(2, '0');
      return `#${hex}${alpha}`;
    }
    return color;
  },
};

// Export individual color palettes for convenience
export const {
  core
} = colors;

export default colors;
