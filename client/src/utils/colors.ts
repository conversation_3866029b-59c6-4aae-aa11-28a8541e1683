/**
 * Color palette for the Kanban RPG application
 * Provides consistent colors across all components
 */

export const colors = {
  // Official Kanban RPG colors
  core: {
    black: "#04080F",
    white: "#F7F9F9",
    blue: "#568EA3",
    green: "#48BF84",
    yellow: "#FFCC00",
    red: "#F06449"
  },

  // Standard gray tones
  gray: {
    50: "#F9FAFB",
    100: "#F3F4F6",
    200: "#E5E7EB",
    300: "#D1D5DB",
    400: "#9CA3AF",
    500: "#6B7280",
    600: "#4B5563",
    700: "#374151",
    800: "#1F2937",
    900: "#111827"
  }
} as const;

// Utility functions for color manipulation
export const colorUtils = {
  /**
   * Get a color with opacity
   */
  withOpacity: (color: string, opacity: number): string => {
    if (color.startsWith('#')) {
      const hex = color.slice(1);
      const alpha = Math.round(opacity * 255).toString(16).padStart(2, '0');
      return `#${hex}${alpha}`;
    }
    return color;
  },
};

// Export individual color palettes for convenience
export const {
  core,
  gray
} = colors;

export default colors;
