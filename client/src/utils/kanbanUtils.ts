import { Task } from '../types';
import { Column, Task as KanbanTask } from '@/components';

/**
 * Transform a backend Task into a KanbanTask
 */
export const transformTaskToKanbanTask = (task: Task): KanbanTask => {
  return {
    id: task.id.toString(), // Convert number to string for Kanban
    title: task.title,
    description: task.description || '',
  };
};

/**
 * Transform backend tasks into Kanban columns based on task status
 */
export const transformTasksToKanbanColumns = (tasks: Task[]): Column[] => {
  // Separate tasks by status
  const todoTasks = tasks.filter(task => !task.completed && !isTaskOverdue(task));
  const inProgressTasks = tasks.filter(task => !task.completed && isTaskOverdue(task));
  const doneTasks = tasks.filter(task => task.completed);

  return [
    {
      id: 'todo',
      title: 'To Do',
      tasks: todoTasks.map(transformTaskToKanbanTask),
    },
    {
      id: 'in-progress',
      title: 'Overdue',
      tasks: inProgressTasks.map(transformTaskToKanbanTask),
    },
    {
      id: 'done',
      title: 'Completed',
      tasks: doneTasks.map(transformTaskToKanbanTask),
    },
  ];
};

/**
 * Check if a task is overdue
 */
const isTaskOverdue = (task: Task): boolean => {
  if (!task.due_date) return false;
  return new Date(task.due_date) < new Date() && !task.completed;
};

/**
 * Transform Kanban task back to backend task ID for operations
 */
export const getTaskIdFromKanbanTask = (kanbanTaskId: string): number => {
  return parseInt(kanbanTaskId, 10);
};

/**
 * Get the task status from column ID
 */
export const getTaskStatusFromColumnId = (columnId: string): { completed: boolean; isOverdue?: boolean } => {
  switch (columnId) {
    case 'todo':
      return { completed: false };
    case 'in-progress':
      return { completed: false, isOverdue: true };
    case 'done':
      return { completed: true };
    default:
      return { completed: false };
  }
};
