import { Task } from '../types';
import { Column, Task as KanbanTask } from '@/components';

/**
 * Transform a backend Task into a KanbanTask
 */
export const transformTaskToKanbanTask = (task: Task): KanbanTask => {
  return {
    id: task.id.toString(), // Convert number to string for Kanban
    title: task.title,
    description: task.description || '',
  };
};

/**
 * Transform backend tasks into Kanban columns based on task status
 */
export const transformTasksToKanbanColumns = (tasks: Task[], inProgressTaskIds?: Set<string>): Column[] => {
  const inProgressIds = inProgressTaskIds || new Set();

  // Separate tasks by status and local in-progress state
  const incompleteTasks = tasks.filter(task => !task.completed);
  const todoTasks = incompleteTasks.filter(task => !inProgressIds.has(task.id.toString()));
  const inProgressTasks = incompleteTasks.filter(task => inProgressIds.has(task.id.toString()));
  const doneTasks = tasks.filter(task => task.completed);

  return [
    {
      id: 'todo',
      title: 'To Do',
      tasks: todoTasks.map(transformTaskToKanbanTask),
    },
    {
      id: 'in-progress',
      title: 'In Progress',
      tasks: inProgressTasks.map(transformTaskToKanbanTask),
    },
    {
      id: 'done',
      title: 'Completed',
      tasks: doneTasks.map(transformTaskToKanbanTask),
    },
  ];
};



/**
 * Transform Kanban task back to backend task ID for operations
 */
export const getTaskIdFromKanbanTask = (kanbanTaskId: string): number => {
  return parseInt(kanbanTaskId, 10);
};

/**
 * Get the task status from column ID
 */
export const getTaskStatusFromColumnId = (columnId: string): { completed: boolean } => {
  switch (columnId) {
    case 'todo':
      return { completed: false };
    case 'in-progress':
      return { completed: false };
    case 'done':
      return { completed: true };
    default:
      return { completed: false };
  }
};
