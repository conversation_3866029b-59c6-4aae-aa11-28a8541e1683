import React from 'react';
import { Header } from '../components/Layout/Header';
import { TaskList } from '../components/Dashboard/TaskList';
import { CharacterSprite } from '../components/Character/CharacterSprite';

export const Dashboard: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Task Management Section */}
          <div className="lg:col-span-2">
            <TaskList />
          </div>

          {/* Character Section */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-lg font-semibold mb-4 text-center">Your Character</h2>
                <CharacterSprite />
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};
