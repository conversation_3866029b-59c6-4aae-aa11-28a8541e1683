import { Dropdown, DropdownTrigger, DropdownContent, DropdownItem, DropdownSeparator, DropdownLabel } from '@DS/core';

const KanbanFilterMenu = () => {
    return (
        <Dropdown>
            <DropdownTrigger asChild>
                <button>Filter Tasks</button>
            </DropdownTrigger>
            <DropdownContent>
                <DropdownLabel>Filter by Status</DropdownLabel>
                <DropdownItem onSelect={() => console.log('Show All')}>
                    Show All
                </DropdownItem>
                <DropdownItem onSelect={() => console.log('To Do')}>
                    To Do
                </DropdownItem>
                <DropdownItem onSelect={() => console.log('In Progress')}>
                    In Progress
                </DropdownItem>
                <DropdownItem onSelect={() => console.log('Done')}>
                    Done
                </DropdownItem>
                <DropdownSeparator />

                <DropdownItem onSelect={() => console.log('Clear Filters')}>
                    Clear Filters
                </DropdownItem>
            </DropdownContent>
        </Dropdown>
    )
}

export default KanbanFilterMenu