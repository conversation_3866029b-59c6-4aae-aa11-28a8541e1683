import { Flex } from '@DS/core';
import React, { useState } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { useDroppable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import KanbanFilterMenu from './KanbanFilterMenu';
import './KanbanBoard.css';

interface Task {
  id: string;
  title: string;
  description: string;
}

interface Column {
  id: string;
  title: string;
  tasks: Task[];
}

// Sample data
const initialData: Column[] = [
  {
    id: 'todo',
    title: 'To Do',
    tasks: [
      { id: 'task-1', title: 'Design homepage', description: 'Create wireframes and mockups' },
      { id: 'task-2', title: 'Setup database', description: 'Configure PostgreSQL' },
    ],
  },
  {
    id: 'in-progress',
    title: 'In Progress',
    tasks: [
      { id: 'task-3', title: 'Build API', description: 'Create REST endpoints' },
    ],
  },
  {
    id: 'done',
    title: 'Done',
    tasks: [
      { id: 'task-4', title: 'Project setup', description: 'Initialize React app' },
    ],
  },
];

// Task Card Component
const TaskCard: React.FC<{ task: Task }> = ({ task }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: task.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="task-card"
    >
      <h4>{task.title}</h4>
      <p>{task.description}</p>
    </div>
  );
};

// Column Component
const KanbanColumn: React.FC<{ column: Column }> = ({ column }) => {
  const { setNodeRef } = useDroppable({
    id: column.id,
  });

  return (
    <div className="kanban-column" ref={setNodeRef}>
      <h3 className="column-title">{column.title}</h3>
      <SortableContext items={column.tasks.map(task => task.id)} strategy={verticalListSortingStrategy}>
        <div className="tasks-container">
          {column.tasks.map((task) => (
            <TaskCard key={task.id} task={task} />
          ))}
        </div>
      </SortableContext>
    </div>
  );
};

const KanbanBoard: React.FC = () => {
  const [columns, setColumns] = useState<Column[]>(initialData);
  const [activeTask, setActiveTask] = useState<Task | null>(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const task = findTaskById(active.id as string);
    setActiveTask(task);
  };

  const handleDragOver = (event: DragOverEvent) => {
    const { active, over } = event;
    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    // Find the columns
    const activeColumn = findColumnByTaskId(activeId);
    const overColumn = findColumnByTaskId(overId) || findColumnById(overId);

    if (!activeColumn || !overColumn) return;

    // Handle cross-column moves only
    if (activeColumn.id !== overColumn.id) {
      setColumns((columns) => {
        const activeItems = activeColumn.tasks;
        const overItems = overColumn.tasks;

        const activeIndex = activeItems.findIndex((item) => item.id === activeId);
        const overIndex = overItems.findIndex((item) => item.id === overId);

        const activeTask = activeItems[activeIndex];

        // Remove from active column
        const newActiveItems = activeItems.filter((item) => item.id !== activeId);

        // Add to over column
        const newOverItems = [...overItems];
        if (overIndex >= 0) {
          // Dropping on a specific task
          newOverItems.splice(overIndex, 0, activeTask);
        } else {
          // Dropping on empty column or at the end
          newOverItems.push(activeTask);
        }

        return columns.map((column) => {
          if (column.id === activeColumn.id) {
            return { ...column, tasks: newActiveItems };
          } else if (column.id === overColumn.id) {
            return { ...column, tasks: newOverItems };
          }
          return column;
        });
      });
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveTask(null);

    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    // Find the columns
    const activeColumn = findColumnByTaskId(activeId);
    const overColumn = findColumnByTaskId(overId) || findColumnById(overId);

    if (!activeColumn || !overColumn) return;

    // Handle same-column reordering
    if (activeColumn.id === overColumn.id) {
      const activeIndex = activeColumn.tasks.findIndex((item) => item.id === activeId);
      const overIndex = activeColumn.tasks.findIndex((item) => item.id === overId);

      if (activeIndex !== overIndex) {
        setColumns((columns) => {
          return columns.map((column) => {
            if (column.id === activeColumn.id) {
              const newTasks = [...column.tasks];
              const [movedTask] = newTasks.splice(activeIndex, 1);
              newTasks.splice(overIndex, 0, movedTask);
              return { ...column, tasks: newTasks };
            }
            return column;
          });
        });
      }
    }
  };

  const findTaskById = (id: string): Task | null => {
    for (const column of columns) {
      const task = column.tasks.find((task) => task.id === id);
      if (task) return task;
    }
    return null;
  };

  const findColumnByTaskId = (taskId: string): Column | null => {
    return columns.find((column) =>
      column.tasks.some((task) => task.id === taskId)
    ) || null;
  };

  const findColumnById = (columnId: string): Column | null => {
    return columns.find((column) => column.id === columnId) || null;
  };

  return (
    <Flex justify="center" className="kanbanBoardContainer">
      <Flex direction="column" align="center" className="kanbanContainer">
        <KanbanFilterMenu />

        <DndContext
          sensors={sensors}
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDragEnd={handleDragEnd}
        >
          <div className="kanban-board">
            {columns.map((column) => (
              <KanbanColumn key={column.id} column={column} />
            ))}
          </div>

          <DragOverlay>
            {activeTask ? (
              <div className="task-card dragging">
                <h4>{activeTask.title}</h4>
                <p>{activeTask.description}</p>
              </div>
            ) : null}
          </DragOverlay>
        </DndContext>
      </Flex>
    </Flex>
  );
};

export default KanbanBoard;