.kanbanBoardContainer {
  flex: 1;
  background-color: var(--color-core-blue);
}

.kanbanContainer {
  padding: 16px;
  min-height: 400px;
  max-width: 1440px;
  flex: 1;
}

/* Kanban Board Styles */
.kanban-board {
  display: flex;
  gap: 24px;
  padding: 20px;
  min-height: 500px;
  width: 100%;
}

.kanban-column {
  width: 320px;
  height: 648px;
  left: 0px;
  top: 0px;
  flex: 1;
  min-width: 300px;
  background-color: var(--color-core-white);
  border-radius: 8px;
  padding: 16px;
  filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));
}

.column-title {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-core-black);
  text-align: center;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--color-gray-200);
}

.tasks-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 200px;
}

.task-card {
  background-color: var(--color-core-white);
  border: 1px solid var(--color-gray-200);
  border-radius: 6px;
  padding: 12px;
  cursor: grab;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.task-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.task-card:active {
  cursor: grabbing;
}

.task-card.dragging {
  opacity: 0.8;
  transform: rotate(5deg);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.task-card h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--color-core-black);
}

.task-card p {
  margin: 0;
  font-size: 12px;
  color: var(--color-gray-500);
  line-height: 1.4;
}