import React from 'react';
import { Navbar } from '@/components/Layout/Navbar';
import HomeKanban from './HomeKanban';
import { Flex } from '../../../DS/src/core';

const Home: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <Flex direction="column" gap="8px" style={{ height: 'calc(100vh - 72px)' }}>
          <div style={{ height: "288px"}}>Placeholder for game content</div>
          <HomeKanban />
      </Flex>
    </div>
  );
};

export default Home