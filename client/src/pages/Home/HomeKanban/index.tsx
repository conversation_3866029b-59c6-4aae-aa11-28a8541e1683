import { Flex } from '@DS/core';
import React from 'react';
import { KanbanContainer, KanbanFilterMenu, Column } from '@/components';
import './styles.css';

// Sample data
const initialData: Column[] = [
  {
    id: 'todo',
    title: 'To Do',
    tasks: [
      { id: 'task-1', title: 'Design homepage', description: 'Create wireframes and mockups' },
      { id: 'task-2', title: 'Setup database', description: 'Configure PostgreSQL' },
    ],
  },
  {
    id: 'in-progress',
    title: 'In Progress',
    tasks: [
      { id: 'task-3', title: 'Build API', description: 'Create REST endpoints' },
    ],
  },
  {
    id: 'done',
    title: 'Done',
    tasks: [
      { id: 'task-4', title: 'Project setup', description: 'Initialize React app' },
    ],
  },
];

const HomeKanban: React.FC = () => {
  return (
    <Flex justify="center" className="kanbanBoardContainer">
      <Flex direction="column" align="center" className="kanbanContainer">
        <KanbanFilterMenu />
        <KanbanContainer initialColumns={initialData} />
      </Flex>
    </Flex>
  );
};

export default HomeKanban;