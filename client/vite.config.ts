import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'node:path'
import { fileURLToPath } from 'url'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@DS/core': path.resolve(__dirname, './DS/src/core'),
      '@DS': path.resolve(__dirname, './DS/src'),
      '@/colors': path.resolve(__dirname, './src/utils/colors'),
    },
  },
  server: {
    port: 3000,
    host: true,
    watch: {
      usePolling: true,
      interval: 100,
      // Don't ignore node_modules to catch changes in local packages
      ignored: ['!**/node_modules/**']
    },
    hmr: {
      // Enable HMR explicitly
      overlay: true,
      clientPort: 3000, // Ensure client connects to the correct port
      timeout: 120000, // Increase timeout
    }
  }
})
